import { SlashCommandBuilder, EmbedBuilder, ActionRowBuilder, StringSelectMenuBuilder, ChannelType } from 'discord.js';
import { COLORS, EMOJIS } from '../../config/constants.js';
import BotConfig from '../../models/BotConfig.js';
import { logger } from '../../utils/logger.js';
import { botLogger } from '../../utils/botLogger.js';

export default {
    data: new SlashCommandBuilder()
        .setName('config-logs')
        .setDescription('Configura os canais de log do bot (apenas administradores)')
        .addSubcommand(subcommand =>
            subcommand
                .setName('definir')
                .setDescription('Define um canal para um tipo específico de log')
                .addStringOption(option =>
                    option
                        .setName('tipo')
                        .setDescription('Tipo de log a configurar')
                        .setRequired(true)
                        .addChoices(
                            { name: '🔧 Admin - Logs administrativos', value: 'admin' },
                            { name: '📢 Público - Logs públicos (vendas)', value: 'public' },
                            { name: '❌ Erro - Logs de erro', value: 'error' },
                            { name: '🛡️ Moderação - Logs de segurança', value: 'moderation' },
                            { name: '⚙️ Sistema - Logs do sistema', value: 'system' },
                            { name: '🐛 Debug - Logs de debug', value: 'debug' },
                            { name: '⚡ Comandos - Logs de comandos', value: 'commands' },
                            { name: '📡 Eventos - Logs de eventos', value: 'events' },
                            { name: '🗄️ Banco - Logs de banco de dados', value: 'database' },
                            { name: '🌐 API - Logs de APIs externas', value: 'api' }
                        )
                )
                .addChannelOption(option =>
                    option
                        .setName('canal')
                        .setDescription('Canal onde os logs serão enviados')
                        .setRequired(true)
                        .addChannelTypes(ChannelType.GuildText)
                )
        )
        .addSubcommand(subcommand =>
            subcommand
                .setName('remover')
                .setDescription('Remove a configuração de um tipo de log')
                .addStringOption(option =>
                    option
                        .setName('tipo')
                        .setDescription('Tipo de log a remover')
                        .setRequired(true)
                        .addChoices(
                            { name: '🔧 Admin', value: 'admin' },
                            { name: '📢 Público', value: 'public' },
                            { name: '❌ Erro', value: 'error' },
                            { name: '🛡️ Moderação', value: 'moderation' },
                            { name: '⚙️ Sistema', value: 'system' },
                            { name: '🐛 Debug', value: 'debug' },
                            { name: '⚡ Comandos', value: 'commands' },
                            { name: '📡 Eventos', value: 'events' },
                            { name: '🗄️ Banco', value: 'database' },
                            { name: '🌐 API', value: 'api' }
                        )
                )
        )
        .addSubcommand(subcommand =>
            subcommand
                .setName('listar')
                .setDescription('Lista todas as configurações de log atuais')
        )
        .addSubcommand(subcommand =>
            subcommand
                .setName('testar')
                .setDescription('Envia mensagens de teste para os canais configurados')
        ),
    
    async execute(interaction) {
        try {
            // Log da execução do comando
            await logger.userAction('Comando config-logs executado', {
                guildId: interaction.guild.id,
                userId: interaction.user.id,
                command: 'config-logs'
            }, {
                subcommand: interaction.options.getSubcommand(),
                user: interaction.user.tag,
                guild: interaction.guild.name
            });

            // Verificação se o usuário é administrador
            if (!interaction.member.permissions.has('Administrator')) {
                await logger.security('Tentativa de uso de comando admin por usuário não autorizado', {
                    guildId: interaction.guild.id,
                    userId: interaction.user.id,
                    command: 'config-logs'
                });

                return await interaction.reply({
                    content: '❌ Apenas administradores podem executar este comando.',
                    ephemeral: true
                });
            }

            const subcommand = interaction.options.getSubcommand();

            switch (subcommand) {
                case 'definir':
                    await handleDefinir(interaction);
                    break;
                case 'remover':
                    await handleRemover(interaction);
                    break;
                case 'listar':
                    await handleListar(interaction);
                    break;
                case 'testar':
                    await handleTestar(interaction);
                    break;
                default:
                    await interaction.reply({
                        content: '❌ Subcomando não reconhecido.',
                        ephemeral: true
                    });
            }

        } catch (error) {
            await logger.logStructured('ERROR', 'COMMAND', 'Erro ao executar comando config-logs', {
                guildId: interaction.guild.id,
                userId: interaction.user.id,
                command: 'config-logs'
            }, {
                error: error.message,
                stack: error.stack,
                subcommand: interaction.options.getSubcommand()
            });
            
            const errorMessage = {
                content: '❌ Erro ao executar comando de configuração de logs.',
                ephemeral: true
            };

            try {
                if (interaction.replied || interaction.deferred) {
                    await interaction.followUp(errorMessage);
                } else {
                    await interaction.reply(errorMessage);
                }
            } catch (replyError) {
                await logger.logStructured('ERROR', 'COMMAND', 'Erro ao responder após falha', {
                    guildId: interaction.guild.id,
                    userId: interaction.user.id
                }, {
                    originalError: error.message,
                    replyError: replyError.message
                });
            }
        }
    }
};

async function handleDefinir(interaction) {
    const tipo = interaction.options.getString('tipo');
    const canal = interaction.options.getChannel('canal');

    try {
        // Busca ou cria configuração
        let config = await BotConfig.findByGuild(interaction.guild.id);
        if (!config) {
            config = new BotConfig({ guildId: interaction.guild.id });
        }

        // Inicializa logChannels se não existir
        if (!config.logChannels) {
            config.logChannels = {};
        }

        // Define o canal para o tipo especificado
        config.logChannels[tipo] = canal.id;
        
        // Mantém compatibilidade com campos antigos
        if (tipo === 'admin') {
            config.adminLogChannelId = canal.id;
        } else if (tipo === 'public') {
            config.publicLogChannelId = canal.id;
        }

        await config.save();

        // Limpa cache do botLogger
        botLogger.clearConfigCache(interaction.guild.id);

        await logger.userAction(`Canal de log ${tipo} configurado`, {
            guildId: interaction.guild.id,
            userId: interaction.user.id
        }, {
            tipo,
            canal: canal.name,
            canalId: canal.id
        });

        const embed = new EmbedBuilder()
            .setColor(COLORS.SUCCESS)
            .setTitle(`${EMOJIS.SUCCESS} Canal de Log Configurado`)
            .setDescription(`Canal de log **${tipo}** configurado com sucesso!`)
            .addFields({
                name: '📍 Canal',
                value: `<#${canal.id}>`,
                inline: true
            })
            .setTimestamp();

        await interaction.reply({ embeds: [embed], ephemeral: true });

    } catch (error) {
        await logger.logStructured('ERROR', 'COMMAND', 'Erro ao definir canal de log', {
            guildId: interaction.guild.id,
            userId: interaction.user.id
        }, {
            error: error.message,
            tipo,
            canalId: canal.id
        });

        await interaction.reply({
            content: '❌ Erro ao configurar canal de log.',
            ephemeral: true
        });
    }
}

async function handleRemover(interaction) {
    const tipo = interaction.options.getString('tipo');

    try {
        const config = await BotConfig.findByGuild(interaction.guild.id);
        if (!config || !config.logChannels || !config.logChannels[tipo]) {
            return await interaction.reply({
                content: `❌ Canal de log **${tipo}** não está configurado.`,
                ephemeral: true
            });
        }

        const canalAnterior = config.logChannels[tipo];
        config.logChannels[tipo] = null;
        
        // Remove compatibilidade com campos antigos
        if (tipo === 'admin') {
            config.adminLogChannelId = null;
        } else if (tipo === 'public') {
            config.publicLogChannelId = null;
        }

        await config.save();

        // Limpa cache do botLogger
        botLogger.clearConfigCache(interaction.guild.id);

        await logger.userAction(`Canal de log ${tipo} removido`, {
            guildId: interaction.guild.id,
            userId: interaction.user.id
        }, {
            tipo,
            canalAnterior
        });

        const embed = new EmbedBuilder()
            .setColor(COLORS.WARNING)
            .setTitle(`${EMOJIS.WARNING} Canal de Log Removido`)
            .setDescription(`Canal de log **${tipo}** foi removido.`)
            .setTimestamp();

        await interaction.reply({ embeds: [embed], ephemeral: true });

    } catch (error) {
        await logger.logStructured('ERROR', 'COMMAND', 'Erro ao remover canal de log', {
            guildId: interaction.guild.id,
            userId: interaction.user.id
        }, {
            error: error.message,
            tipo
        });

        await interaction.reply({
            content: '❌ Erro ao remover canal de log.',
            ephemeral: true
        });
    }
}

async function handleListar(interaction) {
    try {
        const config = await BotConfig.findByGuild(interaction.guild.id);
        
        const embed = new EmbedBuilder()
            .setColor(COLORS.INFO)
            .setTitle(`${EMOJIS.INFO} Configurações de Log`)
            .setDescription('Canais configurados para diferentes tipos de log')
            .setThumbnail(interaction.client.user.displayAvatarURL())
            .setTimestamp();

        const logTypes = [
            { key: 'admin', name: '🔧 Admin', desc: 'Logs administrativos' },
            { key: 'public', name: '📢 Público', desc: 'Logs públicos (vendas)' },
            { key: 'error', name: '❌ Erro', desc: 'Logs de erro' },
            { key: 'moderation', name: '🛡️ Moderação', desc: 'Logs de segurança' },
            { key: 'system', name: '⚙️ Sistema', desc: 'Logs do sistema' },
            { key: 'debug', name: '🐛 Debug', desc: 'Logs de debug' },
            { key: 'commands', name: '⚡ Comandos', desc: 'Logs de comandos' },
            { key: 'events', name: '📡 Eventos', desc: 'Logs de eventos' },
            { key: 'database', name: '🗄️ Banco', desc: 'Logs de banco de dados' },
            { key: 'api', name: '🌐 API', desc: 'Logs de APIs externas' }
        ];

        for (const logType of logTypes) {
            const channelId = config?.logChannels?.[logType.key];
            const value = channelId ? `<#${channelId}>` : '`Não configurado`';
            
            embed.addFields({
                name: `${logType.name}`,
                value: `${logType.desc}\n${value}`,
                inline: true
            });
        }

        // Configurações de logging
        const logSettings = config?.logSettings;
        if (logSettings) {
            embed.addFields({
                name: '⚙️ Configurações',
                value: `**Nível Discord:** ${logSettings.discordLogLevel || 'WARN'}\n**Stack Trace:** ${logSettings.includeStackTrace ? 'Sim' : 'Não'}`,
                inline: false
            });
        }

        await interaction.reply({ embeds: [embed], ephemeral: true });

    } catch (error) {
        await logger.logStructured('ERROR', 'COMMAND', 'Erro ao listar configurações de log', {
            guildId: interaction.guild.id,
            userId: interaction.user.id
        }, {
            error: error.message
        });

        await interaction.reply({
            content: '❌ Erro ao listar configurações de log.',
            ephemeral: true
        });
    }
}

async function handleTestar(interaction) {
    try {
        await interaction.deferReply({ ephemeral: true });

        const config = await BotConfig.findByGuild(interaction.guild.id);
        if (!config || !config.logChannels) {
            return await interaction.editReply({
                content: '❌ Nenhum canal de log configurado para testar.'
            });
        }

        let testesSucesso = 0;
        let testesTotal = 0;

        // Testa cada tipo de canal configurado
        const logTypes = Object.keys(config.logChannels).filter(key => config.logChannels[key]);

        for (const tipo of logTypes) {
            testesTotal++;
            try {
                await botLogger.logToDiscord('INFO', 'SYSTEM', `🧪 Teste de log do tipo: ${tipo}`, {
                    guildId: interaction.guild.id,
                    userId: interaction.user.id
                }, {
                    teste: true,
                    executadoPor: interaction.user.tag,
                    timestamp: new Date().toISOString()
                });
                testesSucesso++;
            } catch (error) {
                await logger.logStructured('WARN', 'COMMAND', `Falha no teste de log ${tipo}`, {
                    guildId: interaction.guild.id
                }, {
                    error: error.message,
                    tipo
                });
            }
        }

        await logger.userAction('Teste de logs executado', {
            guildId: interaction.guild.id,
            userId: interaction.user.id
        }, {
            testesSucesso,
            testesTotal,
            tiposTestados: logTypes
        });

        const embed = new EmbedBuilder()
            .setColor(testesSucesso === testesTotal ? COLORS.SUCCESS : COLORS.WARNING)
            .setTitle(`${EMOJIS.SUCCESS} Teste de Logs Concluído`)
            .setDescription(`**${testesSucesso}/${testesTotal}** canais testados com sucesso`)
            .addFields({
                name: '📊 Resultados',
                value: `Tipos testados: ${logTypes.join(', ')}`,
                inline: false
            })
            .setTimestamp();

        await interaction.editReply({ embeds: [embed] });

    } catch (error) {
        await logger.logStructured('ERROR', 'COMMAND', 'Erro ao testar logs', {
            guildId: interaction.guild.id,
            userId: interaction.user.id
        }, {
            error: error.message
        });

        await interaction.editReply({
            content: '❌ Erro ao executar teste de logs.'
        });
    }
}
